{"version": 3, "file": "service.mjs", "sources": ["../../../../../../packages/components/loading/src/service.ts"], "sourcesContent": ["// @ts-nocheck\nimport { nextTick } from 'vue'\nimport {\n  addClass,\n  getStyle,\n  isClient,\n  isString,\n  removeClass,\n} from '@element-plus/utils'\nimport { createLoadingComponent } from './loading'\n\nimport type { UseNamespaceReturn, UseZIndexReturn } from '@element-plus/hooks'\nimport type { LoadingInstance } from './loading'\nimport type { LoadingOptionsResolved } from '..'\nimport type { LoadingOptions } from './types'\nimport type { AppContext, CSSProperties } from 'vue'\n\nlet fullscreenInstance: LoadingInstance | undefined = undefined\n\nconst Loading = function (options: LoadingOptions = {}): LoadingInstance {\n  if (!isClient) return undefined as any\n\n  const resolved = resolveOptions(options)\n\n  if (resolved.fullscreen && fullscreenInstance) {\n    return fullscreenInstance\n  }\n\n  const instance = createLoadingComponent(\n    {\n      ...resolved,\n      closed: () => {\n        resolved.closed?.()\n        if (resolved.fullscreen) fullscreenInstance = undefined\n      },\n    },\n    Loading._context\n  )\n\n  addStyle(resolved, resolved.parent, instance)\n  addClassList(resolved, resolved.parent, instance)\n\n  resolved.parent.vLoadingAddClassList = () =>\n    addClassList(resolved, resolved.parent, instance)\n\n  /**\n   * add loading-number to parent.\n   * because if a fullscreen loading is triggered when somewhere\n   * a v-loading.body was triggered before and it's parent is\n   * document.body which with a margin , the fullscreen loading's\n   * destroySelf function will remove 'el-loading-parent--relative',\n   * and then the position of v-loading.body will be error.\n   */\n  let loadingNumber: string | null =\n    resolved.parent.getAttribute('loading-number')\n  if (!loadingNumber) {\n    loadingNumber = '1'\n  } else {\n    loadingNumber = `${Number.parseInt(loadingNumber) + 1}`\n  }\n  resolved.parent.setAttribute('loading-number', loadingNumber)\n\n  resolved.parent.appendChild(instance.$el)\n\n  // after instance render, then modify visible to trigger transition\n  nextTick(() => (instance.visible.value = resolved.visible))\n\n  if (resolved.fullscreen) {\n    fullscreenInstance = instance\n  }\n  return instance\n}\n\nconst resolveOptions = (options: LoadingOptions): LoadingOptionsResolved => {\n  let target: HTMLElement\n  if (isString(options.target)) {\n    target =\n      document.querySelector<HTMLElement>(options.target) ?? document.body\n  } else {\n    target = options.target || document.body\n  }\n  return {\n    parent: target === document.body || options.body ? document.body : target,\n    background: options.background || '',\n    svg: options.svg || '',\n    svgViewBox: options.svgViewBox || '',\n    spinner: options.spinner || false,\n    text: options.text || '',\n    fullscreen: target === document.body && (options.fullscreen ?? true),\n    lock: options.lock ?? false,\n    customClass: options.customClass || '',\n    visible: options.visible ?? true,\n    beforeClose: options.beforeClose,\n    closed: options.closed,\n    target,\n  }\n}\n\nconst addStyle = async (\n  options: LoadingOptionsResolved,\n  parent: HTMLElement,\n  instance: LoadingInstance\n) => {\n  // Compatible with the instance data format of vue@3.2.12 and earlier versions #12351\n  const { nextZIndex } =\n    ((instance.vm as any).zIndex as UseZIndexReturn) ||\n    (instance.vm as any)._.exposed.zIndex\n\n  const maskStyle: CSSProperties = {}\n  if (options.fullscreen) {\n    instance.originalPosition.value = getStyle(document.body, 'position')\n    instance.originalOverflow.value = getStyle(document.body, 'overflow')\n    maskStyle.zIndex = nextZIndex()\n  } else if (options.parent === document.body) {\n    instance.originalPosition.value = getStyle(document.body, 'position')\n    /**\n     * await dom render when visible is true in init,\n     * because some component's height maybe 0.\n     * e.g. el-table.\n     */\n    await nextTick()\n    for (const property of ['top', 'left']) {\n      const scroll = property === 'top' ? 'scrollTop' : 'scrollLeft'\n      maskStyle[property] = `${\n        (options.target as HTMLElement).getBoundingClientRect()[property] +\n        document.body[scroll] +\n        document.documentElement[scroll] -\n        Number.parseInt(getStyle(document.body, `margin-${property}`), 10)\n      }px`\n    }\n    for (const property of ['height', 'width']) {\n      maskStyle[property] = `${\n        (options.target as HTMLElement).getBoundingClientRect()[property]\n      }px`\n    }\n  } else {\n    instance.originalPosition.value = getStyle(parent, 'position')\n  }\n  for (const [key, value] of Object.entries(maskStyle)) {\n    instance.$el.style[key] = value\n  }\n}\n\nconst addClassList = (\n  options: LoadingOptions,\n  parent: HTMLElement,\n  instance: LoadingInstance\n) => {\n  // Compatible with the instance data format of vue@3.2.12 and earlier versions #12351\n  const ns =\n    ((instance.vm as any).ns as UseNamespaceReturn) ||\n    (instance.vm as any)._.exposed.ns\n\n  if (\n    !['absolute', 'fixed', 'sticky'].includes(instance.originalPosition.value)\n  ) {\n    addClass(parent, ns.bm('parent', 'relative'))\n  } else {\n    removeClass(parent, ns.bm('parent', 'relative'))\n  }\n  if (options.fullscreen && options.lock) {\n    addClass(parent, ns.bm('parent', 'hidden'))\n  } else {\n    removeClass(parent, ns.bm('parent', 'hidden'))\n  }\n}\n\nLoading._context = null as AppContext | null\nexport default Loading\n"], "names": [], "mappings": ";;;;;;AASA,IAAI,kBAAkB,GAAG,KAAK,CAAC,CAAC;AAC3B,MAAC,OAAO,GAAG,SAAS,OAAO,GAAG,EAAE,EAAE;AACvC,EAAE,IAAI,CAAC,QAAQ;AACf,IAAI,OAAO,KAAK,CAAC,CAAC;AAClB,EAAE,MAAM,QAAQ,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC;AAC3C,EAAE,IAAI,QAAQ,CAAC,UAAU,IAAI,kBAAkB,EAAE;AACjD,IAAI,OAAO,kBAAkB,CAAC;AAC9B,GAAG;AACH,EAAE,MAAM,QAAQ,GAAG,sBAAsB,CAAC;AAC1C,IAAI,GAAG,QAAQ;AACf,IAAI,MAAM,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,CAAC,EAAE,GAAG,QAAQ,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAClE,MAAM,IAAI,QAAQ,CAAC,UAAU;AAC7B,QAAQ,kBAAkB,GAAG,KAAK,CAAC,CAAC;AACpC,KAAK;AACL,GAAG,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;AACvB,EAAE,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAChD,EAAE,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AACpD,EAAE,QAAQ,CAAC,MAAM,CAAC,oBAAoB,GAAG,MAAM,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AACjG,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;AACrE,EAAE,IAAI,CAAC,aAAa,EAAE;AACtB,IAAI,aAAa,GAAG,GAAG,CAAC;AACxB,GAAG,MAAM;AACT,IAAI,aAAa,GAAG,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC5D,GAAG;AACH,EAAE,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;AAChE,EAAE,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC5C,EAAE,QAAQ,CAAC,MAAM,QAAQ,CAAC,OAAO,CAAC,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;AAC5D,EAAE,IAAI,QAAQ,CAAC,UAAU,EAAE;AAC3B,IAAI,kBAAkB,GAAG,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,OAAO,QAAQ,CAAC;AAClB,EAAE;AACF,MAAM,cAAc,GAAG,CAAC,OAAO,KAAK;AACpC,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACrB,EAAE,IAAI,MAAM,CAAC;AACb,EAAE,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AAChC,IAAI,MAAM,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,QAAQ,CAAC,IAAI,CAAC;AACxF,GAAG,MAAM;AACT,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,QAAQ,CAAC,IAAI,CAAC;AAC7C,GAAG;AACH,EAAE,OAAO;AACT,IAAI,MAAM,EAAE,MAAM,KAAK,QAAQ,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,GAAG,MAAM;AAC7E,IAAI,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,EAAE;AACxC,IAAI,GAAG,EAAE,OAAO,CAAC,GAAG,IAAI,EAAE;AAC1B,IAAI,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,EAAE;AACxC,IAAI,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,KAAK;AACrC,IAAI,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,EAAE;AAC5B,IAAI,UAAU,EAAE,MAAM,KAAK,QAAQ,CAAC,IAAI,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC;AAC3F,IAAI,IAAI,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG,KAAK;AAClD,IAAI,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;AAC1C,IAAI,OAAO,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC,OAAO,KAAK,IAAI,GAAG,EAAE,GAAG,IAAI;AACvD,IAAI,WAAW,EAAE,OAAO,CAAC,WAAW;AACpC,IAAI,MAAM,EAAE,OAAO,CAAC,MAAM;AAC1B,IAAI,MAAM;AACV,GAAG,CAAC;AACJ,CAAC,CAAC;AACF,MAAM,QAAQ,GAAG,OAAO,OAAO,EAAE,MAAM,EAAE,QAAQ,KAAK;AACtD,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC,MAAM,IAAI,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;AAC5E,EAAE,MAAM,SAAS,GAAG,EAAE,CAAC;AACvB,EAAE,IAAI,OAAO,CAAC,UAAU,EAAE;AAC1B,IAAI,QAAQ,CAAC,gBAAgB,CAAC,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;AAC1E,IAAI,QAAQ,CAAC,gBAAgB,CAAC,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;AAC1E,IAAI,SAAS,CAAC,MAAM,GAAG,UAAU,EAAE,CAAC;AACpC,GAAG,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,QAAQ,CAAC,IAAI,EAAE;AAC/C,IAAI,QAAQ,CAAC,gBAAgB,CAAC,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;AAC1E,IAAI,MAAM,QAAQ,EAAE,CAAC;AACrB,IAAI,KAAK,MAAM,QAAQ,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE;AAC5C,MAAM,MAAM,MAAM,GAAG,QAAQ,KAAK,KAAK,GAAG,WAAW,GAAG,YAAY,CAAC;AACrE,MAAM,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACpN,KAAK;AACL,IAAI,KAAK,MAAM,QAAQ,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE;AAChD,MAAM,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;AACpF,KAAK;AACL,GAAG,MAAM;AACT,IAAI,QAAQ,CAAC,gBAAgB,CAAC,KAAK,GAAG,QAAQ,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;AACnE,GAAG;AACH,EAAE,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;AACxD,IAAI,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AACpC,GAAG;AACH,CAAC,CAAC;AACF,MAAM,YAAY,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,KAAK;AACpD,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;AACxD,EAAE,IAAI,CAAC,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE;AAClF,IAAI,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC;AAClD,GAAG,MAAM;AACT,IAAI,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC;AACrD,GAAG;AACH,EAAE,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,IAAI,EAAE;AAC1C,IAAI,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;AAChD,GAAG,MAAM;AACT,IAAI,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;AACnD,GAAG;AACH,CAAC,CAAC;AACF,OAAO,CAAC,QAAQ,GAAG,IAAI;;;;"}