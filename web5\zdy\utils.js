var rec_text = ""; // for online rec asr result
var offline_text = ""; // for offline rec asr result
var isfilemode = false; // if it is in file mode
var file_data_array; // array to save file data
var totalsend = 0;
var isRec = false; // 录音状态标志
var isRecordingPaused = false; // 录音暂停状态标志

let sampleBuf = new Int16Array(0);

// 添加录音状态枚举
var RecordingState = RecordingState || {
  STOPPED: "stopped",
  STARTING: "starting",
  RECORDING: "recording",
};

// 替换原来的talking布尔值
let recordingState = RecordingState.STOPPED;

const btnButtonCall = document.querySelector("#btnButtonCall");
const image_call = "url(./static/images/phone_call.png)"; // 电话呼叫的图片URL
const image_down = "url(./static/images/phone_down.png)"; // 电话挂断的图片URL

const wsconnecter = new WebSocketConnectMethod({
  msgHandle: getJsonMessage,
  stateHandle: getConnState,
});

var rec = Recorder({
  type: "pcm",
  bitRate: 16,
  sampleRate: 16000,
  // ======= 以下开启浏览器内建 AEC/NS/AGC =======
  audioTrackSet: {
    echoCancellation: true, // 回声消除（AEC） :contentReference[oaicite:1]{index=1}
    noiseSuppression: true, // 噪声抑制（NS） :contentReference[oaicite:2]{index=2}
    autoGainControl: true, // 自动增益控制（AGC） :contentReference[oaicite:3]{index=3}
    channelCount: 1,         // 单声道录音
  },
  bufferSize: 4096,
  onProcess: recProcess,
});


// 在文件顶部添加唤醒词数组
// const TRIGGER_KEYWORDS = ["小幸运", "小。幸运", "小熊运"];

// 唤醒词配置映射
const WAKE_WORDS_MAP = {
    "小幸运": "你好",
    "小乐": "你好",
};

/**
 * 获取无声调、无分隔符的拼音字符串
 * @param {string} text - 中文文本
 * @returns {string} 拼音字符串
 */
function getPinyinString(text) {
  if (!window.pinyinPro || !window.pinyinPro.pinyin) {
    console.warn('pinyin-pro库未加载，无法进行拼音转换');
    return text;
  }

  const pinyinArray = window.pinyinPro.pinyin(text, {
    toneType: 'none',
    type: 'array',
    nonZh: 'removed',
    multiple: false
  });
  return pinyinArray.join('');
}

/**
 * 通过拼音匹配检查文本是否包含唤醒词
 * @param {string} text - 要检查的文本
 * @returns {Object|null} 匹配到的唤醒词信息，如果没匹配则返回null
 */
function matchWakeWordByPinyin(text) {
    if (!text) return null;

    const textPinyin = getPinyinString(text);

    for (const word of Object.keys(WAKE_WORDS_MAP)) {
        const wordPinyin = getPinyinString(word);
        if (textPinyin.includes(wordPinyin)) {
            return {
                word: word,
                value: WAKE_WORDS_MAP[word],
                matchedText: text
            };
        }
    }

    return null;
}


function start_record() {
  if (recordingState !== RecordingState.STOPPED) {
    console.log(`录音当前状态: ${recordingState}, 等待1秒后重试`);
    sleep(() => start_record(), 1000);
    return;
  }

  try {
    recordingState = RecordingState.STARTING;
    isRecordingPaused = false; // 重置暂停状态
    // 同步全局状态
    window.isRecordingPaused = isRecordingPaused;
    const startResult = ws_start();
    if (startResult === 1) {
      record();
      console.log("录音开始成功");
    } else {
      console.log("录音启动失败");
      record_stop();
    }
  } catch (error) {
    console.error("录音启动出错:", error);
    record_stop();
  }
}

function getJsonMessage(jsonMsg) {
  const data = JSON.parse(jsonMsg.data);
  const rectxt = String(data.text);
  // console.log("--------------------data: " + JSON.stringify(data));
  

  // 使用拼音匹配检查是否包含唤醒词
  const wakeWordMatch = matchWakeWordByPinyin(rectxt);
  console.log("--------------------org_text: ",rectxt,data.mode,data.is_final,wakeWordMatch);
  // if (!wakeWordMatch) {
  //   // 如果拼音匹配失败，回退到原有的关键词匹配
  //   const hasKeyword = TRIGGER_KEYWORDS.some((keyword) =>
  //     rectxt.includes(keyword)
  //   );
  //   if (!hasKeyword) return;
  // } else {
  //   console.log(`通过拼音匹配到唤醒词: ${wakeWordMatch.word} -> ${wakeWordMatch.value}`);
  // }

  if (!wakeWordMatch) {
    return;
  }else{
    console.log(`通过拼音匹配到唤醒词: ${wakeWordMatch.word} -> ${wakeWordMatch.value}`);
  }

  const asrmodel = data.mode;
  // const is_final = data.is_final;
  // const cleanText = rectxt.replace(/ +/g, "");
  
  // 使用switch语句优化条件判断
  switch (asrmodel) {
    case "2pass-offline":
      // offline_text += cleanText + "\n";
      // rec_text = offline_text;

      if (
				userStream &&
				userCamera &&
				userCamera.videoWidth &&
				userCamera.videoHeight
			) {
				// 摄像头已启动，进行视觉识别
				console.log("摄像头已启动，进行视觉识别");
				performVisionAnalysisWithMessage(rectxt);
			} else {
				// 摄像头未启动，直接发送消息
				console.log("摄像头未启动，直接发送消息");
				message_send(rectxt);
			}

      console.log("==================2pass-offline: " + rectxt);

      break;
    case "2pass-online":
      // offline_text += cleanText + "\n";
      // rec_text = offline_text;

      // if (
			// 	userStream &&
			// 	userCamera &&
			// 	userCamera.videoWidth &&
			// 	userCamera.videoHeight
			// ) {
			// 	// 摄像头已启动，进行视觉识别
			// 	console.log("摄像头已启动，进行视觉识别");
			// 	performVisionAnalysisWithMessage(rectxt);
			// } else {
			// 	// 摄像头未启动，直接发送消息
			// 	console.log("摄像头未启动，直接发送消息");
			// 	message_send(message);
			// }

      // console.log("==================2pass-online: " + rectxt);
      // break;
    default:
      // rec_text += rectxt;
  }

  // if (is_final) {
  //   wsconnecter.wsStop();
  // }
}

function getConnState(connState) {
  // console.log('connState------------------------------->',connState);
  if (connState === 0) {
    //on open
    if (isfilemode) {
      start_file_send();
    }
  } else if (connState === 1) {
    //record_stop();
  } else if (connState === 2) {
    record_stop();
    // console.log("connecttion error");
    alert(
      "连接地址" +
        document.getElementById("wssip").value +
        "失败,请检查asr地址和端口。或试试界面上手动授权，再连接。"
    );
  }
}

function start_file_send() {
  sampleBuf = new Uint8Array(file_data_array);

  var chunk_size = 960; // for asr chunk_size [5, 10, 5]

  while (sampleBuf.length >= chunk_size) {
    sendBuf = sampleBuf.slice(0, chunk_size);
    totalsend = totalsend + sampleBuf.length;
    sampleBuf = sampleBuf.slice(chunk_size, sampleBuf.length);
    wsconnecter.wsSend(sendBuf);
  }

  record_stop();
}

function sleep(callback, delay) {
  setTimeout(() => {
    callback();
  }, delay);
}

function record() {
  rec.open(
    function () {
      try {
        rec.start();
        recordingState = RecordingState.RECORDING;
        console.log("录音已开始");
      } catch (error) {
        console.error("录音启动失败:", error);
        record_stop();
      }
    },
    function (error) {
      console.error("录音设备打开失败:", error);
      record_stop();
    }
  );
}

function ws_start() {
  if (recordingState !== RecordingState.STARTING) {
    console.log(`录音状态错误: ${recordingState}`);
    return 0;
  }

  clear();

  const ret = wsconnecter.wsStart();
  if (ret === 1) {
    isRec = true;
    return 1;
  } else {
    console.log("WebSocket连接失败");
    return 0;
  }
}

function record_stop() {
  if (recordingState === RecordingState.STOPPED) {
    console.log("录音已经停止");
    return;
  }

  try {
    isRec = false;
    rec.stop();
    console.log("录音已停止");
  } catch (error) {
    console.error("停止录音时出错:", error);
  } finally {
    recordingState = RecordingState.STOPPED;
    isRec = false;
    isRecordingPaused = false; // 重置暂停状态
    // 同步全局状态
    window.isRec = isRec;
    window.isRecordingPaused = isRecordingPaused;
  }
}

function clear() {
  rec_text = "";
  offline_text = "";
}

function recProcess(
  buffer,
  powerLevel,
  bufferDuration,
  bufferSampleRate,
  newBufferIdx,
  asyncEnd
) {
  if (!isRec) return;

  // const data_48k = buffer[buffer.length - 1];
  // const array_48k = [data_48k];
  // const data_16k = Recorder.SampleData(array_48k, bufferSampleRate, 16000).data;

  // // 使用更高效的数组操作
  // sampleBuf = new Int16Array([...sampleBuf, ...data_16k]);

  // const chunk_size = 960;
  // while (sampleBuf.length >= chunk_size) {
  //   const sendBuf = sampleBuf.slice(0, chunk_size);
  //   wsconnecter.wsSend(sendBuf);
  //   sampleBuf = sampleBuf.slice(chunk_size);
  // }

  const data_48k = buffer[buffer.length - 1];
  const array_48k = new Array(data_48k);
  const data_16k = Recorder.SampleData(array_48k, bufferSampleRate, 16000).data;
  // 正常录音模式：处理并发送数据
  // 将新数据添加到缓冲区（使用更高效的方式）
  const newSampleBuf = new Int16Array(sampleBuf.length + data_16k.length);
  newSampleBuf.set(sampleBuf);
  newSampleBuf.set(data_16k, sampleBuf.length);
  sampleBuf = newSampleBuf;

  // 按块发送数据到ASR服务
  const chunk_size = 960; // for asr chunk_size [5, 10, 5]

  while (sampleBuf.length >= chunk_size) {
    const sendBuf = sampleBuf.slice(0, chunk_size);

    // 发送数据到WebSocket
    if (wsconnecter && wsconnecter.wsSend) {
      wsconnecter.wsSend(sendBuf);
    }

    // 更新缓冲区
    sampleBuf = sampleBuf.slice(chunk_size);
  }

}

function getHotwords() {
  var jsonresult = { 阿里巴巴: 20, 钱心怡: 40 };
  return JSON.stringify(jsonresult);
}

function getAsrMode() {
  return "2pass";
}

function getUseITN() {
  return true;
}


// 添加检查说话状态的函数
function checkSpeakingStatus() {
  fetch("/is_speaking", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      sessionid: parseInt(document.getElementById("sessionid").value),
    }),
    cache: "no-store",
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.code === 0) {
        console.log("当前说话状态:", data.data);

        // 根据说话状态控制麦克风
        if (data.data === true) {
          // 当说话状态为true时，暂停麦克风录音
          if (recordingState === RecordingState.RECORDING) {
            console.log("说话中，暂停录音");
            isRec = false; // 停止音频数据处理
          }
        } else if (data.data === false) {
          // 当说话状态为false时，恢复麦克风录音
          if (recordingState === RecordingState.RECORDING && !isRec) {
            console.log("已停止说话，恢复录音");
            isRec = true; // 恢复音频数据处理
          }
        }
      }
    })
    .catch((error) => console.error("检查说话状态出错:", error));
}

// 启动定期检查
function startSpeakingCheck() {
  // 每1秒检查一次
  setInterval(checkSpeakingStatus, 1000);
}

// 添加恢复录音函数，用于VAD系统调用
function resumeRecording() {
  if (recordingState === RecordingState.RECORDING && isRecordingPaused) {
    console.log("恢复录音数据处理");
    isRec = true; // 恢复音频数据处理
    isRecordingPaused = false; // 更新暂停状态
    // 同步全局状态
    window.isRec = isRec;
    window.isRecordingPaused = isRecordingPaused;
    return true;
  } else if (recordingState === RecordingState.STOPPED) {
    console.log("录音未启动，无法恢复");
    return false;
  } else if (!isRecordingPaused) {
    console.log("录音未暂停，无需恢复");
    return true;
  } else {
    console.log("录音状态异常，无法恢复");
    return false;
  }
}

// 添加暂停录音函数，用于VAD系统调用
function pauseRecording() {
  if (recordingState === RecordingState.RECORDING && !isRecordingPaused) {
    console.log("暂停录音数据处理");
    isRec = false; // 停止音频数据处理
    isRecordingPaused = true; // 更新暂停状态
    // 同步全局状态
    window.isRec = isRec;
    window.isRecordingPaused = isRecordingPaused;
    return true;
  } else if (recordingState === RecordingState.STOPPED) {
    console.log("录音未启动，无法暂停");
    return false;
  } else if (isRecordingPaused) {
    console.log("录音已暂停，无需重复暂停");
    return true;
  } else {
    console.log("录音状态异常，无法暂停");
    return false;
  }
}

// 将函数和状态变量添加到全局作用域，以便其他模块可以调用
window.resumeRecording = resumeRecording;
window.pauseRecording = pauseRecording;
window.isRecordingPaused = isRecordingPaused;
window.isRec = isRec;


